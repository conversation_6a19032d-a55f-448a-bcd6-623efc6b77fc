# Server
PORT=4000
CLIENT_URL=http://localhost:5173

# MongoDB
MONGODB_URI=mongodb://127.0.0.1:27017/civic_issues

# Cloudinary
CLOUDINARY_CLOUD_NAME="dy16rr8k7"
CLOUDINARY_API_KEY="***************"
CLOUDINARY_API_SECRET="SI1_sY1eVYxozE_Jhzcwi045lS0"

# Firebase Admin (Service Account)
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_KEY_WITH_ESCAPED_NEWLINES\n-----END PRIVATE KEY-----\n"

# Email (SMTP)
EMAIL_SMTP_HOST="live.smtp.mailtrap.io"
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USER="<EMAIL>"
EMAIL_SMTP_PASS="8300c791457cb45ff60be76df5bce7ca"
EMAIL_FROM="Civic Issues <<EMAIL>>"

