const express = require("express");
const cors = require("cors");
const morgan = require("morgan");
const mongoose = require("mongoose");
const dotenv = require("dotenv");
import { v2 as cloudinary } from "cloudinary";

dotenv.config();

const app = express();

// Config
const PORT = process.env.PORT || 4000;
const CLIENT_URL = process.env.CLIENT_URL || "http://localhost:5173";

// Middlewares
app.use(cors({ origin: CLIENT_URL, credentials: true }));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(morgan("dev"));

// DB connect
(async () => {
  const uri =
    process.env.MONGODB_URI || "mongodb://127.0.0.1:27017/civic_issues";
  try {
    await mongoose.connect(uri, { autoIndex: true });
    console.log("MongoDB connected");
  } catch (err) {
    console.error("MongoDB connection error", err.message);
    process.exit(1);
  }
})();

// Routes
app.get("/api/health", (req, res) => res.json({ ok: true }));
app.use("/api/reports", require("./routes/reports"));
app.use("/api/admin", require("./routes/admin"));

// Start
app.listen(PORT, () => {
  console.log(`Server listening on http://localhost:${PORT}`);
});

(async function () {
  // Configuration
  cloudinary.config({
    cloud_name: "dy16rr8k7",
    api_key: "766967857821887",
    api_secret: "<your_api_secret>", // Click 'View API Keys' above to copy your API secret
  });

  // Upload an image
  const uploadResult = await cloudinary.uploader
    .upload(
      "https://res.cloudinary.com/demo/image/upload/getting-started/shoes.jpg",
      {
        public_id: "shoes",
      }
    )
    .catch((error) => {
      console.log(error);
    });

  console.log(uploadResult);

  // Optimize delivery by resizing and applying auto-format and auto-quality
  const optimizeUrl = cloudinary.url("shoes", {
    fetch_format: "auto",
    quality: "auto",
  });

  console.log(optimizeUrl);

  // Transform the image: auto-crop to square aspect_ratio
  const autoCropUrl = cloudinary.url("shoes", {
    crop: "auto",
    gravity: "auto",
    width: 500,
    height: 500,
  });

  console.log(autoCropUrl);
})();
