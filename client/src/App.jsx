import { Link, Route, Routes } from "react-router-dom";
import "./App.css";
import MapView from "./pages/MapView";
import ReportForm from "./pages/ReportForm";
import AdminDashboard from "./pages/AdminDashboard";
import MyReports from "./pages/MyReports";
import React from "react";
import { Cloudinary } from "@cloudinary/url-gen";
import { auto } from "@cloudinary/url-gen/actions/resize";
import { autoGravity } from "@cloudinary/url-gen/qualifiers/gravity";
import { AdvancedImage } from "@cloudinary/react";

function App() {
  return (
    <div>
      <nav
        style={{
          display: "flex",
          gap: 12,
          padding: 12,
          borderBottom: "1px solid #eee",
        }}
      >
        <Link to="/">Map</Link>
        <Link to="/report">Report</Link>
        <Link to="/my">My Reports</Link>
        <Link to="/admin">Admin</Link>
      </nav>
      <div style={{ padding: 12 }}>
        <Routes>
          <Route path="/" element={<MapView />} />
          <Route path="/report" element={<ReportForm />} />
          <Route path="/my" element={<MyReports />} />
          <Route path="/admin" element={<AdminDashboard />} />
        </Routes>
      </div>
    </div>
  );
}
const App = () => {
  const cld = new Cloudinary({ cloud: { cloudName: "dy16rr8k7" } });
  const img = cld
    .image("cld-sample-5")
    .format("auto")
    .quality("auto")
    .resize(auto().gravity(autoGravity()).width(500).height(500)); 
};

export default App;
